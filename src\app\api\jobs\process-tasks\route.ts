import { NextRequest, NextResponse } from 'next/server'
import { taskProcessorService } from '@/lib/services/taskProcessorService'
import { taskScheduler } from '@/lib/jobs/taskScheduler'
import { requireAuth } from '@/lib/auth'
import { UserRole } from 'generated-prisma'

/**
 * 手动触发任务处理
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限 - 只有WM和PM角色可以触发
    const authResult = await requireAuth(request, [UserRole.WM, UserRole.PM])
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    console.log(`🔧 用户 ${authResult.user?.name} 触发任务处理`)

    // 检查是否已有任务在处理
    if (taskScheduler.isCurrentlyProcessing()) {
      return NextResponse.json({
        success: false,
        message: '任务处理正在进行中，请稍后再试',
        isProcessing: true
      })
    }

    // 执行任务处理
    const executionLog = await taskScheduler.executeTaskProcessing()

    return NextResponse.json({
      success: executionLog.status === 'completed',
      message: executionLog.status === 'completed' ? '任务处理完成' : '任务处理失败',
      data: {
        executionId: executionLog.id,
        processedCount: executionLog.processedCount,
        failedCount: executionLog.failedCount,
        duration: executionLog.duration,
        status: executionLog.status,
        error: executionLog.error
      }
    })

  } catch (error) {
    console.error('手动任务处理失败:', error)
    return NextResponse.json(
      { 
        success: false,
        error: '任务处理失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

/**
 * 获取任务处理状态和统计信息
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户权限
    const authResult = await requireAuth(request, [UserRole.WM, UserRole.PM, UserRole.Triage])
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    // 获取处理统计信息
    const processingStats = await taskProcessorService.getProcessingStats()

    // 获取任务处理器状态
    const taskProcessorStatus = taskScheduler.getSchedulerStatus()

    // 获取最近执行状态
    const lastExecutionStatus = taskScheduler.getLastExecutionStatus()

    // 获取执行日志
    const executionLogs = taskScheduler.getExecutionLogs(10)

    // 获取统计信息
    const statistics = taskScheduler.getStatistics()

    return NextResponse.json({
      success: true,
      data: {
        processingStats,
        taskProcessorStatus,
        lastExecutionStatus,
        executionLogs,
        statistics,
        isCurrentlyProcessing: taskScheduler.isCurrentlyProcessing()
      }
    })

  } catch (error) {
    console.error('获取任务处理状态失败:', error)
    return NextResponse.json(
      { 
        success: false,
        error: '获取状态失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}
