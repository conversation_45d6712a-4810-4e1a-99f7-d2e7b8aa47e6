import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { getCurrentUser } from '@/lib/auth';
import { TaskEmailData } from '@/types/task' 

 

interface DistributeEmailsRequest {
  emails: TaskEmailData[]; 
}

export async function POST(request: NextRequest) {
  try {
    const body: DistributeEmailsRequest = await request.json();
    const { emails } = body;

    if (!emails || emails.length === 0) {
      return NextResponse.json(
        { error: '没有要分发的邮件' },
        { status: 400 }
      );
    }

    // 从cookie获取当前用户
    const user = await getCurrentUser(request);
    
    if (!user) {
      return NextResponse.json(
        { error: '用户未登录' },
        { status: 401 }
      );
    }

    // 验证用户权限 - 只有WM用户可以分发
    if (!user.roles.includes('WM')) {
      return NextResponse.json(
        { error: '只有WM用户可以使用分发功能' },
        { status: 403 }
      );
    }
 
    const processedEmails = []; 

    // 处理每封邮件
    for (const email of emails) {
      try {
        // 创建任务记录
        const task = await prisma.task.create({
          data: {
            title: `${email.subject}`,
            description: `来自 ${email.sender} 的邮件`,
            status: 'PENDING',
            priority: 'MEDIUM',
            createdById: user.id,
            metadata: {
              source: 'outlook',
              itemId: email.itemId,
              sender: email.sender,
              subject: email.subject,
              receivedTime: email.receivedTime,
              // 保存完整的邮件信息
              from: email.from,
              to: email.to,
              cc: email.cc,
              date: email.date,
              body: email.body,
              attachments: email.attachments?.map(att => ({
                name: att.name,
                size: att.size,
                contentType: att.contentType,
                isInline: att.isInline,
                id: att.id,
                encoding: att.encoding,
                // 注意：不保存content到数据库，因为base64内容可能很大
                // content: att.content
              })),
              hasFullContent: true, // 标记已包含完整内容
            },
          },
        });
  
        processedEmails.push({
          emailId: email.itemId,
          taskId: task.id,
          status: 'success',
        });
         
      } catch (emailError) {
        console.error(`处理邮件 ${email.itemId} 失败:`, emailError);
        processedEmails.push({
          emailId: email.itemId,
          error: '处理失败',
          status: 'error',
        });
      }
    }
 

    return NextResponse.json({
      success: true, 
      totalCount: emails.length,
      results: processedEmails, 
    });
  } catch (error) {
    console.error('邮件分发失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  }
}
