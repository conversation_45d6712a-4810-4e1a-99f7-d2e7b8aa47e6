{"name": "triage", "version": "0.1.0", "private": true, "scripts": {"_dev": "next dev --port 3100 --turbopack", "dev": "node server.js", "build": "npm run generate && next build", "start": "next start", "ts-lint": "tsc --noEmit", "lint": "npm run ts-lint && next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "outlook-cert": "npx office-addin-dev-certs install", "generate": "npx prisma generate", "db-push": "npm run generate && npx prisma db push"}, "prisma": {}, "dependencies": {"@ckeditor/ckeditor5-react": "^9.5.0", "@microlink/react-json-view": "^1.26.2", "@microsoft/office-js": "^1.1.110", "@prisma/client": "^6.12.0", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "@typedorm/common": "^1.15.4", "@typedorm/core": "^1.15.4", "@types/office-js-preview": "^1.0.616", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "ckeditor5": "^45.2.1", "ckeditor5-premium-features": "^45.2.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "ews-javascript-api": "^0.15.3", "html-pdf": "^3.0.1", "jose": "^6.0.11", "jsonrepair": "^3.13.0", "langfuse": "^3.38.4", "lodash": "^4.17.21", "lucide-react": "^0.523.0", "marked": "^15.0.12", "mime-types": "^3.0.1", "mobx": "^6.13.7", "mobx-react-lite": "^4.1.0", "next": "15.3.4", "openai": "^5.8.2", "pdf-lib": "^1.17.1", "pdf2pic": "^3.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "reflect-metadata": "^0.1.14", "sharp": "^0.34.3", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "winston": "^3.17.0", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/html-pdf": "^3.0.3", "@types/jest": "^30.0.0", "@types/lodash": "^4.17.16", "@types/mime-types": "^2.1.4", "@types/node": "^20", "@types/office-js": "^1.0.514", "@types/office-runtime": "^1.0.35", "@types/react": "^19", "@types/react-dom": "^19", "@types/shelljs": "^0.8.15", "@types/winston": "^2.4.4", "eslint": "^9", "eslint-config-next": "15.3.4", "eslint-plugin-office-addins": "^4.0.3", "jest": "^30.0.4", "office-addin-cli": "^2.0.3", "office-addin-debugging": "^6.0.3", "office-addin-dev-certs": "^2.0.3", "office-addin-lint": "^3.0.3", "office-addin-manifest": "^2.0.3", "office-addin-prettier-config": "^2.0.1", "prisma": "^6.12.0", "shelljs": "^0.10.0", "tailwindcss": "^4", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5"}}