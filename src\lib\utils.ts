import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: string | Date) {
  return new Intl.DateTimeFormat('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(date))
}

/**
 * 格式化中文日期（年月日格式）
 * @param date 日期对象或日期字符串
 * @returns 格式化后的中文日期，如 "2024年1月15日"
 */
export function formatChineseDate(date: string | Date): string {
  const d = new Date(date)
  const year = d.getFullYear()
  const month = d.getMonth() + 1
  const day = d.getDate()
  return `${year}年${month}月${day}日`
}

/**
 * 获取指定日期的第二天并格式化为中文日期
 * @param date 日期对象或日期字符串
 * @returns 第二天的中文日期格式
 */
export function getNextDayChineseDate(date: string | Date): string {
  const d = new Date(date)
  d.setDate(d.getDate() + 1)
  return formatChineseDate(d)
}

export function formatFileSize(bytes: number) {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export function getStatusColor(status: string) {
  switch (status.toLowerCase()) {
    case 'completed':
    case 'success':
      return 'badge-success'
    case 'processing':
    case 'in_progress':
      return 'badge-warning'
    case 'failed':
    case 'error':
      return 'badge-danger'
    case 'pending':
      return 'badge-info'
    default:
      return 'badge-gray'
  }
}

export function getStatusText(status: string) {
  switch (status) {
    case 'RECEIVED':
      return '已接收'
    case 'PROCESSING':
      return '处理中'
    case 'CONFIRMED':
      return '已确认'
    case 'FORWARDED':
      return '已转发'
    case 'COMPLETED':
      return '已完成'
    case 'PENDING':
      return '待处理'
    case 'IN_PROGRESS':
      return '进行中'
    case 'FAILED':
      return '失败'
    case 'SKIPPED':
      return '已跳过'
    default:
      return status
  }
}

export function getRoleText(role: string) {
  switch (role) {
    case 'WM':
      return 'Workflow Manager'
    case 'Triage':
      return 'Triage'
    case 'PM':
      return 'Project Manager'
    case 'DE':
      return 'Data Entry'
    case 'QC':
      return 'Quality Control'
    default:
      return role
  }
}

export function getRolesText(roles: string[] | string): string {
  if (typeof roles === 'string') {
    return getRoleText(roles)
  }

  if (Array.isArray(roles)) {
    return roles.map(role => getRoleText(role)).join(', ')
  }

  return ''
}

export function getReportTypeText(type: string) {
  switch (type) {
    case 'INITIAL':
      return '首次报告'
    case 'FOLLOW_UP':
      return '随访报告'
    case 'QUERY_RESPONSE':
      return '质疑回复'
    default:
      return type
  }
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    
    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

