import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth'
import { UserRole } from 'generated-prisma'
import { apiLog } from '@/lib/logger'
import fs from 'fs'
import path from 'path'

/**
 * 获取日志文件列表和内容
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now()
  apiLog.request('GET', '/api/logs')
  
  try {
    // 验证用户权限 - 只有管理员可以查看日志
    const authResult = await requireAuth(request, [UserRole.WM, UserRole.PM])
    if (!authResult.success) {
      apiLog.response('GET', '/api/logs', authResult.status, Date.now() - startTime, {
        error: 'Authentication failed'
      })
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const { searchParams } = new URL(request.url)
    const logType = searchParams.get('type') || 'app' // app, error, combined
    const lines = parseInt(searchParams.get('lines') || '100')
    const search = searchParams.get('search') || ''

    const logDir = path.join(process.cwd(), 'logs')
    
    // 检查日志目录是否存在
    if (!fs.existsSync(logDir)) {
      return NextResponse.json({
        success: false,
        error: '日志目录不存在'
      }, { status: 404 })
    }

    // 获取日志文件列表
    const logFiles = fs.readdirSync(logDir).filter(file => file.endsWith('.log'))
    
    let logContent = ''
    let fileName = ''

    // 根据类型选择日志文件
    switch (logType) {
      case 'error':
        fileName = 'error.log'
        break
      case 'combined':
        fileName = 'combined.log'
        break
      case 'app':
      default:
        fileName = 'app.log'
        break
    }

    const logFilePath = path.join(logDir, fileName)
    
    if (fs.existsSync(logFilePath)) {
      // 读取日志文件
      const fullContent = fs.readFileSync(logFilePath, 'utf-8')
      const allLines = fullContent.split('\n').filter(line => line.trim())
      
      // 如果有搜索条件，过滤日志行
      let filteredLines = allLines
      if (search) {
        filteredLines = allLines.filter(line => 
          line.toLowerCase().includes(search.toLowerCase())
        )
      }
      
      // 获取最后N行
      const recentLines = filteredLines.slice(-lines)
      logContent = recentLines.join('\n')
    }

    // 获取日志文件信息
    const logFileStats = logFiles.map(file => {
      const filePath = path.join(logDir, file)
      const stats = fs.statSync(filePath)
      return {
        name: file,
        size: stats.size,
        modified: stats.mtime,
        sizeFormatted: formatFileSize(stats.size)
      }
    })

    apiLog.response('GET', '/api/logs', 200, Date.now() - startTime, {
      logType,
      lines: recentLines?.length || 0,
      hasSearch: !!search
    })

    return NextResponse.json({
      success: true,
      data: {
        logType,
        fileName,
        content: logContent,
        totalLines: logContent.split('\n').length,
        files: logFileStats,
        searchTerm: search
      }
    })

  } catch (error) {
    apiLog.error('GET', '/api/logs', error as Error, {
      duration: Date.now() - startTime
    })
    return NextResponse.json(
      { 
        success: false,
        error: '获取日志失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

/**
 * 清空指定类型的日志文件
 */
export async function DELETE(request: NextRequest) {
  const startTime = Date.now()
  apiLog.request('DELETE', '/api/logs')
  
  try {
    // 验证用户权限 - 只有管理员可以清空日志
    const authResult = await requireAuth(request, [UserRole.WM])
    if (!authResult.success) {
      apiLog.response('DELETE', '/api/logs', authResult.status, Date.now() - startTime, {
        error: 'Authentication failed'
      })
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.status }
      )
    }

    const { searchParams } = new URL(request.url)
    const logType = searchParams.get('type') || 'app'

    const logDir = path.join(process.cwd(), 'logs')
    
    let fileName = ''
    switch (logType) {
      case 'error':
        fileName = 'error.log'
        break
      case 'combined':
        fileName = 'combined.log'
        break
      case 'app':
      default:
        fileName = 'app.log'
        break
    }

    const logFilePath = path.join(logDir, fileName)
    
    if (fs.existsSync(logFilePath)) {
      // 清空日志文件（保留文件但清空内容）
      fs.writeFileSync(logFilePath, '')
      
      apiLog.response('DELETE', '/api/logs', 200, Date.now() - startTime, {
        logType,
        fileName,
        action: 'cleared'
      })

      return NextResponse.json({
        success: true,
        message: `${fileName} 日志文件已清空`
      })
    } else {
      return NextResponse.json({
        success: false,
        error: '日志文件不存在'
      }, { status: 404 })
    }

  } catch (error) {
    apiLog.error('DELETE', '/api/logs', error as Error, {
      duration: Date.now() - startTime
    })
    return NextResponse.json(
      { 
        success: false,
        error: '清空日志失败',
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    )
  }
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
