// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/generated-prisma"
  // output   = "../src/generated/prisma"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  WM
  Triage
  PM
}

enum ProjectRole {
  WM
  Triage
  PM
  DE
  QC
}

enum ReportType {
  INITIAL
  FOLLOW_UP
  QUERY_RESPONSE
}

enum ReportStatus {
  RECEIVED
  PROCESSING
  CONFIRMED
  FORWARDED
  COMPLETED
}

enum EmailTemplateType {
  CONFIRMATION
  SAFETY_NOTIFICATION
  INTERNAL_FORWARD
  WM_FORWARD
} 

 
enum TaskStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum TaskPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum ReportAttachmentType {
  MAIL_EML // 原始邮件
  SAE_EXTRACTION // AI提取结果
  SAE_PDF // 合并后的PDF
  SUB_MAIL_EML // 生成的回复邮件 
}

model User {
  id            Int      @id @default(autoincrement())
  email         String   @unique
  name          String
  password      String? // 明文密码存储
  domainAccount String // 域账号字段（必填）
  roles         Json // 用户角色数组，存储UserRole枚举值
  department    String?
  isActive      Boolean  @default(true)
  dailyReportMin Int?     @default(5) // 每日能处理报告数量最小值
  dailyReportMax Int?     @default(10) // 每日能处理报告数量最大值
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // 关联关系
  userProjects     UserProject[]
  createdTasks     Task[]             @relation("CreatedBy")
  assignedTasks    Task[]             @relation("AssignedTo")
  qcTasks    Task[]             @relation("QcTo")
  deTasks    Task[]             @relation("DeTo")

  @@map("users")
}

model Project {
  id          Int      @id @default(autoincrement())
  projectCode String   @unique
  projectName String
  sponsor     String?
  studyTitle  String?  @db.Text
  pvEmail     String?
  description String?  @db.Text
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  userProjects   UserProject[]
  saeReports     SAEReport[]
  emailTemplates EmailTemplate[]
  tasks          Task[]

  @@map("projects")
}

model UserProject {
  id            Int         @id @default(autoincrement())
  userId        Int
  projectId     Int
  roleInProject ProjectRole
  isPrimary     Boolean     @default(false)
  createdAt     DateTime    @default(now())

  // 关联关系
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  @@unique([userId, projectId, roleInProject])
  @@map("user_projects")
}

model Task {
  id           Int          @id @default(autoincrement())
  title        String
  description  String?      @db.Text
  status       TaskStatus   @default(PENDING)
  priority     TaskPriority @default(MEDIUM)
  projectId    Int?         // 项目ID，可选
  assignedToId Int?
  qcToId Int?
  deToId Int?
  createdById  Int 

  metadata     Json // 存储来源信息，如邮件ID、发件人等
  lastError    String?     @db.Text
  lastErrorTime DateTime?
  completedAt  DateTime?
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt

  // 关联关系
  project    Project? @relation(fields: [projectId], references: [id], onDelete: Cascade)
  assignedTo User?    @relation("AssignedTo", fields: [assignedToId], references: [id])
  qcTo User?    @relation("QcTo", fields: [qcToId], references: [id])
  deTo User?    @relation("DeTo", fields: [deToId], references: [id])
  createdBy  User     @relation("CreatedBy", fields: [createdById], references: [id])
  @@map("tasks")
}
 

model EmailTemplate {
  id               Int               @id @default(autoincrement())
  templateName     String
  templateType     EmailTemplateType
  subjectTemplate  String?           @db.Text
  bodyTemplate     String            @db.Text
  recipientEmails  Json? // 收件人邮箱地址数组
  ccEmails         Json? // 抄送人邮箱地址数组
  attachmentConfig Json?
  variableMapping  Json?
  isDefault        Boolean           @default(false)
  projectId        Int?
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt

  // 关联关系
  project   Project?   @relation(fields: [projectId], references: [id], onDelete: SetNull) 

  @@map("email_templates")
}

model SAEReport {
  id              Int          @id @default(autoincrement())
  reportUuid      String       @unique
  projectId       Int
  protocolNumber  String?
  subjectNumber   String? 
  eventName       String?
  reportType      ReportType
  learnedDate     DateTime?
  seriousness     String?
  eventType       String?
  causality       String? 
  originalEmailId String?
  status          ReportStatus @default(RECEIVED) 
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt

  // 关联关系
  project                Project                 @relation(fields: [projectId], references: [id]) 
  attachments            ReportAttachment[]  

  @@map("sae_reports")
}

model ReportAttachment {
  id               Int              @id @default(autoincrement())
  reportId         Int
  fileName         String
  originalFileName String?
  filePath         String
  type             ReportAttachmentType
  fileSize         BigInt?
  fileType         String?
  mimeType         String?
  fileHash         String?
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt

  // 关联关系
  report SAEReport @relation(fields: [reportId], references: [id], onDelete: Cascade)

  @@map("report_attachments")
}
  
 

 // AI相关
model AIPrompt {
  id            Int      @id @default(autoincrement())
  promptName    String
  promptType    String // SAE_EXTRACTION, PDF_ANALYSIS, EMAIL_CLASSIFICATION
  promptContent String   @db.Text
  isActive      Boolean  @default(true)
  version       String   @default("1.0")
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@map("ai_prompts")
}
