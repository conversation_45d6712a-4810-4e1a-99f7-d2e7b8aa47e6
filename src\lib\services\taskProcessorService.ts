import { prisma } from '@/lib/db'
import { EWSMailService } from './ewsMailService'
import { PDFProcessorService } from './pdfProcessorService'
import { projectService } from './projectService'
import { AIExtractor } from './aiExtractor'
import { saeReportService } from './saeReportService'
import { fileAttachmentService } from './fileAttachmentService'
import { emailTemplateService } from './emailTemplateService'
import { notificationService } from './notificationService'
import { personnelAssignmentService } from './personnelAssignmentService'
import { TaskStatus, TaskPriority, UserRole, ReportAttachmentType, ReportStatus } from 'generated-prisma'
import type { Prisma } from 'generated-prisma';
import type { TaskMetadata } from '@/types/task';
import { formatChineseDate, getNextDayChineseDate } from '@/lib/utils' 

type Task = Prisma.TaskGetPayload<{
  include: {
    createdBy: true; 
  }
}>
 

export interface TaskProcessingResult {
  success: boolean
  processedCount: number
  failedCount: number
  results: Array<{
    taskId: number
    success: boolean
    error?: string
    assignedProject?: string
    assignedPersonnel?: {
      processor?: string
      dataEntry?: string
      qualityControl?: string
    }
  }>
}

export interface ProcessingStats {
  totalTasks: number
  pendingTasks: number
  processingTasks: number
  completedTasks: number
  failedTasks: number
}

export class TaskProcessorService {
  private aiExtractor: AIExtractor
  private isProcessing = false

  constructor() {
    this.aiExtractor = new AIExtractor()
  }

  /**
   * 处理所有待处理任务
   */
  async processAllPendingTasks(): Promise<TaskProcessingResult> {
    if (this.isProcessing) {
      console.log('任务处理正在进行中，跳过本次执行')
      return {
        success: false,
        processedCount: 0,
        failedCount: 0,
        results: []
      }
    }

    this.isProcessing = true
    console.log('🚀 开始处理待处理任务...')

    try {
      // 查询待处理任务
      const pendingTasks = await this.getPendingTasks()
      console.log(`找到 ${pendingTasks.length} 个待处理任务`)

      if (pendingTasks.length === 0) {
        return {
          success: true,
          processedCount: 0,
          failedCount: 0,
          results: []
        }
      }

      const results: TaskProcessingResult['results'] = []
      let processedCount = 0
      let failedCount = 0

      // 逐个处理任务
      for (const task of pendingTasks) {
        try {
          console.log(`处理任务 ${task.id}: ${task.title}`)
          const result = await this.processSingleTask(task)
          
          results.push({
            taskId: task.id,
            success: result.success,
            error: result.error,
            assignedProject: result.assignedProject,
            assignedPersonnel: result.assignedPersonnel
          })

          if (result.success) {
            processedCount++
          } else {
            failedCount++
          }

        } catch (error) {
          console.error(`任务 ${task.id} 处理失败:`, error)
          results.push({
            taskId: task.id,
            success: false,
            error: error instanceof Error ? error.message : '未知错误'
          })
          failedCount++
        }
      }

      console.log(`✅ 任务处理完成: 成功 ${processedCount}, 失败 ${failedCount}`)

      return {
        success: true,
        processedCount,
        failedCount,
        results
      }

    } catch (error) {
      console.error('批量任务处理失败:', error)
      return {
        success: false,
        processedCount: 0,
        failedCount: 0,
        results: []
      }
    } finally {
      this.isProcessing = false
    }
  }

  /**
   * 处理单个任务
   */
  private async processSingleTask(task: Task): Promise<{
    success: boolean
    error?: string
    assignedProject?: string
    assignedPersonnel?: {
      processor?: string
      dataEntry?: string
      qualityControl?: string
    }
  }> {
    try {
      // 1. 获取任务创建人信息
      const creator = await prisma.user.findUnique({
        where: { id: task.createdById },
        select: {
          domainAccount: true,
          password: true,
          email: true,
          name: true
        }
      })

      if (!creator || !creator.domainAccount || !creator.password) {
        throw new Error('任务创建人信息不完整或缺少域账号/密码')
      }
      const metadata = task.metadata as any as TaskMetadata

      // 2. 检查邮件附件
      if(!metadata.attachments || metadata.attachments.length < 1){
        throw new Error('邮件中没有附件')
      }

      // 过滤非内联附件
      const attachments = metadata.attachments.filter((a: any) => !a.isInline && a.content)
      if(attachments.length < 1){
        throw new Error('邮件中没有可用的附件内容')
      }

      let attachment = attachments?.find((a: any) => a.name.endsWith('.pdf'))

      const aiFindAttachment = await this.aiExtractor.findMainAttachment(
        metadata.subject,
        metadata.body ||'',
        metadata.sender,
        attachments.map((a: any) => a.name)
      )
      if(!aiFindAttachment.saeAttachmentName){
        throw new Error('AI未能自动识别邮件中的附件')
      }
      attachment = attachments.find((a: any) => a.name === aiFindAttachment.saeAttachmentName)
      if(!attachment){
        throw new Error('AI自动识别邮件附件名称异常')
      }
      const pdf = await PDFProcessorService.processPdfWithOCR(Buffer.from(attachment.content, 'base64'), attachment.name)
      if(!pdf.isPageNumberContinuity){
        throw new Error('pdf页码不完整')
      }
      if(!pdf.hasSignature){
        throw new Error('pdf未签名')
      }
      if(!pdf.isPdfComplete){
        throw new Error('pdf内容不完整')
      }

      const extractedText = pdf.extractedText || '';
      const saeInfo = await this.aiExtractor.extractSAEInfo(extractedText)
      const matchedProject = await projectService.matchProject(saeInfo.data.protocolNumber);
      if(!matchedProject){
        throw new Error(`${saeInfo.data.protocolNumber} -- 项目匹配失败`)
      } 
       
      console.log('matchedProject :>> ', matchedProject);

      // 5. 人员分配
      const assignedPersonnel = await personnelAssignmentService.assignPersonnel(matchedProject?.id)

      // 6. 更新任务信息
      const updateData: any = {
        status: TaskStatus.IN_PROGRESS,
        updatedAt: new Date()
      }
 
      // 分配项目
      updateData.projectId = matchedProject.id

      // 分配人员
      if (assignedPersonnel.processor) {
        updateData.assignedToId = assignedPersonnel.processor.id
      }
      if (assignedPersonnel.dataEntry) {
        updateData.deToId = assignedPersonnel.dataEntry.id
      }
      if (assignedPersonnel.qualityControl) {
        updateData.qcToId = assignedPersonnel.qualityControl.id
      }
 
      await prisma.task.update({
        where: { id: task.id },
        data: updateData
      })

      // 7. 创建SAE报告
      console.log('创建SAE报告...')
      const saeReport = await saeReportService.createSAEReport({
        saeInfo: saeInfo.data,
        projectId: matchedProject.id,
        originalEmailId: metadata.itemId
      })

      // 8. 生成邮件正文PDF
      console.log('生成邮件正文PDF...')
      const emailBodyPDF = await fileAttachmentService.htmlToPDFBuffer(metadata.body || '')

      // 9. 合并所有附件到一个PDF
      console.log('合并PDF附件...')
      const mergedPDF = await fileAttachmentService.mergeAttachmentsToPDF(
        emailBodyPDF,
        attachments.map((a: any) => ({
          name: a.name,
          contentType: a.contentType,
          size: a.size,
          content: a.content,
          isInline: a.isInline || false
        })),
        saeReport.id
      )

      // 10. 保存合并后的PDF到附件表
      await fileAttachmentService.saveAttachment({
        reportId: saeReport.id,
        fileName: mergedPDF.fileName,
        filePath: mergedPDF.filePath,
        type: ReportAttachmentType.SAE_PDF,
        fileSize: mergedPDF.fileSize,
        fileType: 'application/pdf',
        mimeType: 'application/pdf'
      })

      // 11. 准备邮件模板变量
      const templateVariables = {
        protocolNumber: saeInfo.data.protocolNumber,
        subjectNumber: saeInfo.data.subjectNumber,
        eventName: saeInfo.data.eventName,
        seriousness: saeInfo.data.seriousness,
        eventType: saeInfo.data.eventType,
        causality: saeInfo.data.causality,
        learnedDate: saeInfo.data.learnedDate,
        centerName: saeInfo.data.centerName,
        projectCode: matchedProject.projectCode,
        projectName: matchedProject.projectName,
        sponsor: matchedProject.sponsor || undefined,
        pvEmail: matchedProject.pvEmail || undefined,
        processorName: assignedPersonnel.processor?.name,
        processorEmail: assignedPersonnel.processor?.email,
        dataEntryName: assignedPersonnel.dataEntry?.name,
        dataEntryEmail: assignedPersonnel.dataEntry?.email,
        qualityControlName: assignedPersonnel.qualityControl?.name,
        qualityControlEmail: assignedPersonnel.qualityControl?.email,
        reportUuid: saeReport.reportUuid
      }

      // 12. 生成所有类型的邮件
      console.log('生成邮件模板...')
      const generatedEmails = await emailTemplateService.generateAllEmailTypes(
        matchedProject.id,
        templateVariables
      )

      // 13. 创建EML文件
      console.log('创建EML文件...')
      const emlFiles = await emailTemplateService.createAllEMLFiles(
        generatedEmails,
        saeReport.id
      )

      // 14. 保存EML文件到附件表
      for (const [_templateType, emlFile] of emlFiles) {
        await fileAttachmentService.saveAttachment({
          reportId: saeReport.id,
          fileName: emlFile.fileName,
          filePath: emlFile.filePath,
          type: ReportAttachmentType.SUB_MAIL_EML,
          fileSize: emlFile.fileSize,
          fileType: 'message/rfc822',
          mimeType: 'message/rfc822'
        })
      }

      // 15. 发送基于角色的通知邮件
      console.log('发送通知邮件...')
      const notificationResult = await notificationService.sendRoleBasedNotifications(
        saeReport.id,
        matchedProject.id,
        {
          processor: assignedPersonnel.processor ? {
            id: assignedPersonnel.processor.id,
            name: assignedPersonnel.processor.name,
            email: assignedPersonnel.processor.email,
            roles: assignedPersonnel.processor.roles as UserRole[]
          } : undefined,
          dataEntry: assignedPersonnel.dataEntry ? {
            id: assignedPersonnel.dataEntry.id,
            name: assignedPersonnel.dataEntry.name,
            email: assignedPersonnel.dataEntry.email,
            roles: assignedPersonnel.dataEntry.roles as UserRole[]
          } : undefined,
          qualityControl: assignedPersonnel.qualityControl ? {
            id: assignedPersonnel.qualityControl.id,
            name: assignedPersonnel.qualityControl.name,
            email: assignedPersonnel.qualityControl.email,
            roles: assignedPersonnel.qualityControl.roles as UserRole[]
          } : undefined
        },
        emlFiles,
        {
          domainAccount: creator.domainAccount,
          password: creator.password
        }
      )

      console.log('通知邮件发送结果:', notificationResult)

      // 16. 更新SAE报告状态
      await saeReportService.updateReportStatus(saeReport.id, ReportStatus.PROCESSING)

      // 17. 完成任务
      await prisma.task.update({
        where: { id: task.id },
        data: {
          status: TaskStatus.COMPLETED,
          updatedAt: new Date()
        }
      })

      return {
        success: true,
        assignedProject: matchedProject?.projectCode,
        assignedPersonnel: {
          processor: assignedPersonnel.processor?.name,
          dataEntry: assignedPersonnel.dataEntry?.name,
          qualityControl: assignedPersonnel.qualityControl?.name
        }
      }

    } catch (error) {
      console.error(`任务 ${task.id} 处理失败:`, error)
      
      // 更新任务状态为失败
      await prisma.task.update({
        where: { id: task.id },
        data: {
          status: TaskStatus.PENDING, // 保持待处理状态，便于重试
          lastError: error instanceof Error ? error.message : '未知错误',
          lastErrorTime: new Date().toISOString()
        }
      }).catch(console.error)

      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  /**
   * 获取待处理任务
   */
  private async getPendingTasks() {
    return await prisma.task.findMany({
      where: {
        status: {
          in: [TaskStatus.PENDING]
        }
      },
      include: {
        createdBy: true
      },
      orderBy: {
        createdAt: 'asc'
      },
      take: 10 // 限制每次处理的任务数量
    })
  }
  
  /**
   * 获取处理统计信息
   */
  async getProcessingStats(): Promise<ProcessingStats> {
    const stats = await prisma.task.groupBy({
      by: ['status'],
      _count: {
        status: true
      }
    })

    const result: ProcessingStats = {
      totalTasks: 0,
      pendingTasks: 0,
      processingTasks: 0,
      completedTasks: 0,
      failedTasks: 0
    }

    stats.forEach(stat => {
      result.totalTasks += stat._count.status

      switch (stat.status) {
        case TaskStatus.PENDING:
          result.pendingTasks = stat._count.status
          break
        case TaskStatus.IN_PROGRESS:
          result.processingTasks = stat._count.status
          break
        case TaskStatus.COMPLETED:
          result.completedTasks = stat._count.status
          break
        case TaskStatus.CANCELLED:
          result.failedTasks = stat._count.status
          break
      }
    })

    return result
  }

  /**
   * 检查是否正在处理
   */
  isCurrentlyProcessing(): boolean {
    return this.isProcessing
  }
 
}

// 导出单例实例
export const taskProcessorService = new TaskProcessorService()
