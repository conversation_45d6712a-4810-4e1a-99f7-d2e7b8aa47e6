{"level":"error","message":"Error message","service":"triage-app","timestamp":"2025-07-24 11:42:52"}
{"action":"task_error","error":"Test error","level":"error","message":"❌ 任务处理失败","service":"triage-app","stack":"Error: Test error\n    at Object.<anonymous> (D:\\work\\triage\\src\\__tests__\\logger.test.ts:81:21)\n    at Promise.finally.completed (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1499:10)\n    at _callCircusTest (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at run (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1920:21)\n    at jestAdapter (D:\\work\\triage\\node_modules\\jest-circus\\build\\runner.js:101:19)\n    at runTestInternal (D:\\work\\triage\\node_modules\\jest-runner\\build\\testWorker.js:272:16)\n    at runTest (D:\\work\\triage\\node_modules\\jest-runner\\build\\testWorker.js:340:7)\n    at Object.worker (D:\\work\\triage\\node_modules\\jest-runner\\build\\testWorker.js:494:12)","step":"pdf_processing","taskId":12345,"timestamp":"2025-07-24 11:42:52","title":"Test Task"}
{"action":"email_send_error","error":"SMTP connection failed","level":"error","message":"❌ 邮件发送失败","service":"triage-app","smtpServer":"smtp.example.com","stack":"Error: SMTP connection failed\n    at Object.<anonymous> (D:\\work\\triage\\src\\__tests__\\logger.test.ts:107:21)\n    at Promise.finally.completed (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1499:10)\n    at _callCircusTest (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at run (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1920:21)\n    at jestAdapter (D:\\work\\triage\\node_modules\\jest-circus\\build\\runner.js:101:19)\n    at runTestInternal (D:\\work\\triage\\node_modules\\jest-runner\\build\\testWorker.js:272:16)\n    at runTest (D:\\work\\triage\\node_modules\\jest-runner\\build\\testWorker.js:340:7)\n    at Object.worker (D:\\work\\triage\\node_modules\\jest-runner\\build\\testWorker.js:494:12)","subject":"Test Email","timestamp":"2025-07-24 11:42:52","to":"<EMAIL>"}
{"action":"api_error","error":"Database connection failed","level":"error","message":"❌ API错误","method":"POST","service":"triage-app","stack":"Error: Database connection failed\n    at Object.<anonymous> (D:\\work\\triage\\src\\__tests__\\logger.test.ts:133:21)\n    at Promise.finally.completed (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1499:10)\n    at _callCircusTest (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at run (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1920:21)\n    at jestAdapter (D:\\work\\triage\\node_modules\\jest-circus\\build\\runner.js:101:19)\n    at runTestInternal (D:\\work\\triage\\node_modules\\jest-runner\\build\\testWorker.js:272:16)\n    at runTest (D:\\work\\triage\\node_modules\\jest-runner\\build\\testWorker.js:340:7)\n    at Object.worker (D:\\work\\triage\\node_modules\\jest-runner\\build\\testWorker.js:494:12)","timestamp":"2025-07-24 11:42:52","url":"/api/test","userId":123}
{"action":"db_error","error":"Connection timeout","level":"error","message":"❌ 数据库错误","operation":"create","service":"triage-app","stack":"Error: Connection timeout\n    at Object.<anonymous> (D:\\work\\triage\\src\\__tests__\\logger.test.ts:146:21)\n    at Promise.finally.completed (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1559:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1499:10)\n    at _callCircusTest (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1009:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:949:3)\n    at _runTestsForDescribeBlock (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:839:13)\n    at _runTestsForDescribeBlock (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at _runTestsForDescribeBlock (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:829:11)\n    at run (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:757:3)\n    at runAndTransformResultsToJestFormat (D:\\work\\triage\\node_modules\\jest-circus\\build\\jestAdapterInit.js:1920:21)\n    at jestAdapter (D:\\work\\triage\\node_modules\\jest-circus\\build\\runner.js:101:19)\n    at runTestInternal (D:\\work\\triage\\node_modules\\jest-runner\\build\\testWorker.js:272:16)\n    at runTest (D:\\work\\triage\\node_modules\\jest-runner\\build\\testWorker.js:340:7)\n    at Object.worker (D:\\work\\triage\\node_modules\\jest-runner\\build\\testWorker.js:494:12)","table":"tasks","timestamp":"2025-07-24 11:42:52"}
{"level":"error","message":"Test error handling Test error message","service":"triage-app","stack":"Error stack trace...","timestamp":"2025-07-24 11:42:52"}
{"level":"error","message":"String error message","service":"triage-app","timestamp":"2025-07-24 11:42:52"}
