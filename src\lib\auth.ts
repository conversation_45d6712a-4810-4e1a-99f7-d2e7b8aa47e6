import { SignJWT, jwtVerify } from 'jose'
import { cookies } from 'next/headers'
import { NextRequest } from 'next/server'
import { prisma } from './db'
import { User, UserRole } from 'generated-prisma'
import { ExchangeService, WebCredentials, Uri, ExchangeVersion, WellKnownFolderName, FolderView } from 'ews-javascript-api';

const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || 'your-secret-key')
const JWT_ALGORITHM = 'HS256'

export interface JWTPayload {
  userId: number
  email: string
  roles: UserRole[]
  name: string
  iat?: number
  exp?: number
  [key: string]: any
}

export interface AuthUser {
  id: number
  email: string
  name: string
  roles: UserRole[]
  department?: string
  dailyReportMin?: number
  dailyReportMax?: number
}

/**
 * 生成JWT Token
 */
export async function generateToken(user: AuthUser): Promise<string> {
  const payload: JWTPayload = {
    userId: user.id,
    email: user.email,
    roles: user.roles,
    name: user.name
  }

  return await new SignJWT(payload)
    .setProtectedHeader({ alg: JWT_ALGORITHM })
    .setIssuedAt()
    .setExpirationTime('7d') // 7天过期
    .sign(JWT_SECRET)
}

/**
 * 验证JWT Token
 */
export async function verifyToken(token: string): Promise<JWTPayload | null> {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET)
    return payload as JWTPayload
  } catch (error) {
    console.error('Token verification failed:', error)
    return null
  }
}


// EWS 验证函数 - 使用域账号进行验证
export async function validateEWSCredentials(domainAccount: string, password: string): Promise<boolean> {
  try {
    const service = new ExchangeService(ExchangeVersion.Exchange2016);
    // 设置凭据
    service.Credentials = new WebCredentials(domainAccount, password);     
    service.Url = new Uri(process.env.EWS_URL || '');
    
    const view = new FolderView(1); // 返回最多 1 个文件夹
    const result = await service.FindFolders(WellKnownFolderName.MsgFolderRoot, view);
    console.log("✅ 认证成功",result); 
    return true; 
  } catch (error) {
    console.error('EWS 验证失败:', error);
    return false;
  }
}


/**
 * 从请求中获取当前用户
 */
export async function getCurrentUser(request?: NextRequest): Promise<AuthUser | null> {
  try {
    let token: string | undefined

    if (request) {
      // 从请求头获取token
      token = request.headers.get('Authorization')?.replace('Bearer ', '')
      
      // 如果没有Authorization头，尝试从cookie获取
      if (!token) {
        token = request.cookies.get('auth-token')?.value
      }
    } else {
      // 服务端组件中从cookies获取
      const cookieStore = await cookies()
      token = cookieStore.get('auth-token')?.value
    }

    if (!token) {
      return null
    }

    const payload = await verifyToken(token)
    if (!payload) {
      return null
    }

    // 从数据库获取最新用户信息
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        email: true,
        name: true,
        roles: true,
        department: true,
        dailyReportMin: true,
        dailyReportMax: true,
        isActive: true
      }
    })

    if (!user || !user.isActive) {
      return null
    }

    return {
      id: user.id,
      email: user.email,
      name: user.name,
      roles: user.roles as UserRole[],
      department: user.department || undefined,
      dailyReportMin: user.dailyReportMin || undefined,
      dailyReportMax: user.dailyReportMax || undefined
    }
  } catch (error) {
    console.error('Get current user failed:', error)
    return null
  }
}

/**
 * 通过邮箱识别用户身份
 */
export async function identifyUserByEmail(email: string): Promise<AuthUser | null> {
  try {
    const user = await prisma.user.findUnique({
      where: { email: email.toLowerCase() },
      select: {
        id: true,
        email: true,
        name: true,
        roles: true,
        department: true,
        dailyReportMin: true,
        dailyReportMax: true,
        isActive: true
      }
    })

    if (!user || !user.isActive) {
      return null
    }

    return {
      id: user.id,
      email: user.email,
      name: user.name,
      roles: user.roles as UserRole[],
      department: user.department || undefined,
      dailyReportMin: user.dailyReportMin || undefined,
      dailyReportMax: user.dailyReportMax || undefined
    }
  } catch (error) {
    console.error('Identify user by email failed:', error)
    return null
  }
}

/**
 * 检查用户权限
 */
export function hasPermission(userRoles: UserRole[], requiredRoles: UserRole[]): boolean {
  return requiredRoles.some(role => userRoles.includes(role))
}

/**
 * 验证请求权限并返回用户信息
 */
export async function requireAuth(
  request: NextRequest,
  requiredRoles?: UserRole[]
): Promise<{
  success: boolean
  user?: AuthUser
  error?: string
  status?: number
}> {
  try {
    // 获取当前用户
    const user = await getCurrentUser(request)

    if (!user) {
      return {
        success: false,
        error: '未授权访问',
        status: 401
      }
    }

    // 检查角色权限
    if (requiredRoles && requiredRoles.length > 0) {
      if (!hasPermission(user.roles, requiredRoles)) {
        return {
          success: false,
          error: '权限不足',
          status: 403
        }
      }
    }

    return {
      success: true,
      user
    }

  } catch (error) {
    console.error('Auth verification failed:', error)
    return {
      success: false,
      error: '认证验证失败',
      status: 500
    }
  }
}

/**
 * 检查用户是否有项目访问权限
 */
export async function hasProjectAccess(userId: number, projectId: number): Promise<boolean> {
  try {
    const userProject = await prisma.userProject.findFirst({
      where: {
        userId,
        projectId
      }
    })

    return !!userProject
  } catch (error) {
    console.error('Check project access failed:', error)
    return false
  }
}

/**
 * 获取用户可访问的项目列表
 */
export async function getUserProjects(userId: number) {
  try {
    const userProjects = await prisma.userProject.findMany({
      where: { userId },
      include: {
        project: {
          select: {
            id: true,
            projectCode: true,
            projectName: true,
            sponsor: true,
            studyTitle: true,
            pvEmail: true,
            isActive: true
          }
        }
      }
    })

    return userProjects
      .filter(up => up.project.isActive)
      .map(up => ({
        ...up.project,
        userRole: up.roleInProject,
        isPrimary: up.isPrimary
      }))
  } catch (error) {
    console.error('Get user projects failed:', error)
    return []
  }
}

/**
 * 权限装饰器 - 用于API路由
 */
export function withAuth(requiredRoles?: UserRole[]) {
  return function (handler:  (request: NextRequest, context: any) => void) {
    return async function (request: NextRequest, context: any) {
      const user = await getCurrentUser(request)
      
      if (!user) {
        return Response.json(
          { success: false, error: 'Unauthorized' },
          { status: 401 }
        )
      }

      if (requiredRoles && !hasPermission(user.roles, requiredRoles)) {
        return Response.json(
          { success: false, error: 'Forbidden' },
          { status: 403 }
        )
      }

      // 将用户信息添加到请求上下文
      ;(request as any).user = user

      return handler(request, context)
    }
  }
}

/**
 * 项目权限装饰器
 */
export function withProjectAuth() {
  return function (handler: (request: NextRequest, context: any) => void) {
    return async function (request: NextRequest, context: any) {
      const user = await getCurrentUser(request)
      
      if (!user) {
        return Response.json(
          { success: false, error: 'Unauthorized' },
          { status: 401 }
        )
      }

      // 从URL参数或请求体中获取projectId
      const url = new URL(request.url)
      const projectId = url.searchParams.get('projectId') || 
                       context.params?.projectId ||
                       (await request.json().catch(() => ({})))?.projectId

      if (projectId && !await hasProjectAccess(user.id, parseInt(projectId))) {
        return Response.json(
          { success: false, error: 'Project access denied' },
          { status: 403 }
        )
      }

      ;(request as any).user = user

      return handler(request, context)
    }
  }
}

/**
 * 设置认证Cookie
 */
export async function setAuthCookie(token: string) {
  const cookieStore = await cookies()
  cookieStore.set('auth-token', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 7 * 24 * 60 * 60, // 7天
    path: '/'
  })
}

/**
 * 清除认证Cookie
 */
export async function clearAuthCookie() {
  const cookieStore = await cookies()
  cookieStore.delete('auth-token')
}
